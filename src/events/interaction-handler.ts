import {
    ButtonInteraction,
    Client,
    Events,
    ModalSubmitInteraction,
    StringSelectMenuInteraction
} from "discord.js";
import {
    handleCartButton,
    handleCartRemoveButton,
    handleTicketButton,
    handleQuantityModal,
    handleCheckoutQuantityModal,
    handleShopQuantityModal,
    handlePaymentSelection,
    handleImageModal,
    handleCustomFooterModal,
    handleCustomLogoModal,
    handleCategorySelection,
    handleListingSelection,
    handleCheckoutPaymentSelection,
    handleShopCategorySelection,
    handleShopListingSelection,
    handleShopPaymentSelection
} from "../handlers";
import { handleAvailabilitySelection } from "../handlers/select-menus/availability-select-handler";
import { handleAdminButton, handleDeleteConfirmation } from "../handlers/buttons/admin-handler";

export default (client: Client): void => {
    client.on(Events.InteractionCreate, async (interaction) => {
        try {
            if (interaction.isButton()) {
                await handleButtonInteraction(interaction);
            } else if (interaction.isStringSelectMenu()) {
                await handleSelectMenuInteraction(interaction);
            } else if (interaction.isModalSubmit()) {
                await handleModalSubmit(interaction);
            }
        } catch (error) {
            console.error('Error handling interaction:', error);

            // Try to respond with an error message if possible
            try {
                if (interaction.isRepliable()) {
                    if (!interaction.replied && !interaction.deferred) {
                        await interaction.reply({
                            content: 'An error occurred while processing your request.',
                            ephemeral: true
                        });
                    } else if (interaction.deferred) {
                        await interaction.followUp({
                            content: 'An error occurred while processing your request.',
                            ephemeral: true
                        });
                    }
                }
            } catch (responseError) {
                console.error('Error sending error response:', responseError);
            }
        }
    });
};

/**
 * Route button interactions to appropriate handlers
 */
async function handleButtonInteraction(interaction: ButtonInteraction) {
    const customIdParts = interaction.customId.split('_');
    const action = customIdParts[0];

    switch (action) {
        case 'cart':
            if (customIdParts[1] === 'remove') {
                await handleCartRemoveButton(interaction, customIdParts[2]);
            } else {
                await handleCartButton(interaction, customIdParts[1], customIdParts[2]);
            }
            break;
        case 'ticket':
            await handleTicketButton(interaction, customIdParts[1], customIdParts[2]);
            break;
        case 'admin':
            await handleAdminButton(interaction, customIdParts[1], customIdParts[2]);
            break;
        case 'confirm':
            if (customIdParts[1] === 'delete') {
                await handleDeleteConfirmation(interaction, 'confirm', customIdParts[2]);
            }
            break;
        case 'cancel':
            if (customIdParts[1] === 'delete') {
                await handleDeleteConfirmation(interaction, 'cancel', customIdParts[2]);
            }
            break;
        default:
            console.warn(`Unknown button action: ${action}`);
            await interaction.reply({
                content: 'Unknown button action.',
                ephemeral: true
            });
    }
}

/**
 * Route select menu interactions to appropriate handlers
 */
async function handleSelectMenuInteraction(interaction: StringSelectMenuInteraction) {
    const customIdParts = interaction.customId.split('_');
    const action = customIdParts[0];
    const selectedValue = interaction.values[0];

    switch (action) {
        case 'payment':
            await handlePaymentSelection(interaction, customIdParts[1], selectedValue);
            break;
        case 'availability':
            // Handle the new format: availability_listingId_userId
            const listingId = customIdParts[1];
            await handleAvailabilitySelection(interaction, listingId, selectedValue);
            break;
        case 'checkout':
            if (customIdParts[1] === 'category') {
                // checkout_category_userId_discordUserId
                const userId = customIdParts[2];
                const discordUserId = customIdParts[3];
                await handleCategorySelection(interaction, userId, discordUserId);
            } else if (customIdParts[1] === 'listing') {
                // checkout_listing_userId_discordUserId
                const userId = customIdParts[2];
                const discordUserId = customIdParts[3];
                await handleListingSelection(interaction, userId, discordUserId);
            } else if (customIdParts[1] === 'payment') {
                // checkout_payment_listingId_userId_discordUserId
                const listingId = customIdParts[2];
                const userId = customIdParts[3];
                const discordUserId = customIdParts[4];
                await handleCheckoutPaymentSelection(interaction, listingId, userId, discordUserId);
            }
            break;
        case 'shop':
            if (customIdParts[1] === 'category') {
                // shop_category_userId or shop_category_userId_interactingUserId
                const userId = customIdParts[2];
                const interactingUserId = customIdParts[3];
                await handleShopCategorySelection(interaction, userId);
            } else if (customIdParts[1] === 'listing') {
                // shop_listing_userId_interactingUserId
                const userId = customIdParts[2];
                const interactingUserId = customIdParts[3];
                await handleShopListingSelection(interaction, userId, interactingUserId);
            } else if (customIdParts[1] === 'payment') {
                // shop_payment_listingId_interactingUserId
                const listingId = customIdParts[2];
                const interactingUserId = customIdParts[3];
                await handleShopPaymentSelection(interaction, listingId, interactingUserId);
            }
            break;
        default:
            console.warn(`Unknown select menu action: ${action}`);
            await interaction.reply({
                content: 'Unknown select menu action.',
                ephemeral: true
            });
    }
}

/**
 * Route modal submissions to appropriate handlers
 */
async function handleModalSubmit(interaction: ModalSubmitInteraction) {
    const customIdParts = interaction.customId.split('_');
    const action = customIdParts[0];
    const subAction = customIdParts[1];

    if (action === 'cart' && subAction === 'quantity') {
        const listingId = customIdParts[2];
        const paymentId = customIdParts[3];
        await handleQuantityModal(interaction, listingId, paymentId);
    } else if (action === 'checkout' && subAction === 'quantity') {
        // checkout_quantity_listingId_paymentId_userId_discordUserId
        const listingId = customIdParts[2];
        const paymentId = customIdParts[3];
        const targetUserId = customIdParts[4];
        const discordUserId = customIdParts[5];
        await handleCheckoutQuantityModal(interaction, listingId, paymentId, targetUserId, discordUserId);
    } else if (action === 'shop' && subAction === 'quantity') {
        // shop_quantity_listingId_paymentId
        const listingId = customIdParts[2];
        const paymentId = customIdParts[3];
        await handleShopQuantityModal(interaction, listingId, paymentId);
    } else if (action === 'corner' && subAction === 'image' && customIdParts[2] === 'modal') {
        const listingId = customIdParts[3];
        await handleImageModal(interaction, 'corner', listingId);
    } else if (action === 'main' && subAction === 'image' && customIdParts[2] === 'modal') {
        const listingId = customIdParts[3];
        await handleImageModal(interaction, 'main', listingId);
    } else if (action === 'custom' && subAction === 'footer' && customIdParts[2] === 'modal') {
        const listingId = customIdParts[3];
        await handleCustomFooterModal(interaction, listingId);
    } else if (action === 'custom' && subAction === 'logo' && customIdParts[2] === 'modal') {
        const listingId = customIdParts[3];
        await handleCustomLogoModal(interaction, listingId);
    } else {
        console.warn(`Unknown modal action: ${action}_${subAction}`);
        await interaction.reply({
            content: 'Unknown modal action.',
            ephemeral: true
        });
    }
}
