import {
    ChannelType,
    Client,
    Guild,
    PermissionFlagsBits,
    TextChannel,
    User
} from "discord.js";
import { CartSummaryService } from "./cart-summary-service";
import { GuildRoleHelper } from "../utils/guildRoleHelper";
import { TranscriptService } from "./transcript-service";

export class ChannelService {
    /**
     * Find existing cart channel for a user
     * Looks for channels with "cart" in name and checks if the first message tags the user
     * Supports both Discord user IDs and UUIDs
     */
    static async findExistingCartChannel(
        guild: Guild,
        userId: string,
        discordUserId?: string
    ): Promise<TextChannel | null> {
        try {
            console.log(`Looking for existing cart channel for user ${userId}`);

            // Fetch all channels to ensure cache is up to date
            await guild.channels.fetch();

            // Get all text channels that contain "cart" in the name
            const channels = guild.channels.cache.filter(channel =>
                channel.type === ChannelType.GuildText &&
                channel.name.includes('cart')
            );

            console.log(`Found ${channels.size} channels with 'cart' in name`);

            for (const [, channel] of channels) {
                const textChannel = channel as TextChannel;
                console.log(`Checking channel: ${textChannel.name}`);

                try {
                    // We need to find the FIRST message in the channel, not just recent ones
                    // Fetch all messages and get the oldest one
                    const firstMessage = await this.getFirstMessage(textChannel);

                    if (!firstMessage) {
                        console.log(`Could not get first message in ${textChannel.name}`);
                        continue;
                    }

                    // Check if the first message mentions this user (try both Discord ID and UUID)
                    console.log(`First message in ${textChannel.name}: "${firstMessage.content.substring(0, 100)}..."`);

                    // Check for Discord user mention
                    const hasDiscordMention = discordUserId && firstMessage.content.includes(`<@${discordUserId}>`);
                    // Check for UUID mention (for checkout flow)
                    const hasUuidMention = firstMessage.content.includes(userId);
                    // Check for legacy Discord ID mention (for backward compatibility)
                    const hasLegacyMention = /^\d{17,19}$/.test(userId) && firstMessage.content.includes(`<@${userId}>`);

                    if (hasDiscordMention || hasUuidMention || hasLegacyMention) {
                        console.log(`✅ Found existing cart channel for user ${userId}: ${textChannel.name}`);
                        return textChannel;
                    }

                    console.log(`❌ First message in ${textChannel.name} does not mention user ${userId}`);
                } catch (error) {
                    // Skip channels we can't read
                    console.log(`Could not read messages in channel ${textChannel.name}: ${error}`);
                    continue;
                }
            }

            console.log(`No existing cart channel found for user ${userId}`);
            return null;
        } catch (error) {
            console.error('Error finding existing cart channel:', error);
            return null;
        }
    }

    /**
     * Create a private cart channel for a user (only if one doesn't exist)
     */
    static async createCartChannel(
        guild: Guild,
        client: Client,
        userId: string,
        username: string
    ): Promise<TextChannel | null> {
        try {
            // Get configured cart access roles
            const cartAccessRoles = await GuildRoleHelper.getGuildRoles(guild.id, 'cart_access');

            // Base permission overwrites
            const permissionOverwrites = [
                {
                    id: guild.id, // @everyone role
                    deny: [PermissionFlagsBits.ViewChannel]
                },
                {
                    id: client.user!.id,
                    allow: [
                        PermissionFlagsBits.ViewChannel,
                        PermissionFlagsBits.SendMessages,
                        PermissionFlagsBits.ReadMessageHistory
                    ]
                }
            ];

            // Only add user permissions if we have a valid Discord user ID
            if (discordUserId && /^\d{17,19}$/.test(discordUserId)) {
                permissionOverwrites.push({
                    id: discordUserId,
                    allow: [
                        PermissionFlagsBits.ViewChannel,
                        PermissionFlagsBits.SendMessages,
                        PermissionFlagsBits.ReadMessageHistory
                    ]
                });
            } else if (/^\d{17,19}$/.test(userId)) {
                // Fallback: if userId is a valid Discord ID, use it
                permissionOverwrites.push({
                    id: userId,
                    allow: [
                        PermissionFlagsBits.ViewChannel,
                        PermissionFlagsBits.SendMessages,
                        PermissionFlagsBits.ReadMessageHistory
                    ]
                });
            }

            // Add configured cart access roles
            cartAccessRoles.forEach(roleId => {
                permissionOverwrites.push({
                    id: roleId,
                    allow: [
                        PermissionFlagsBits.ViewChannel,
                        PermissionFlagsBits.SendMessages,
                        PermissionFlagsBits.ReadMessageHistory
                    ]
                });
            });

            const channel = await guild.channels.create({
                name: `🛒┊cart-${username}-${Date.now().toString().slice(-4)}`,
                type: ChannelType.GuildText,
                permissionOverwrites
            });

            return channel as TextChannel;
        } catch (error) {
            console.error('Error creating cart channel:', error);
            return null;
        }
    }

    /**
     * Get or create a cart channel for a user (ensures only one cart channel per user)
     */
    static async getOrCreateCartChannel(
        guild: Guild,
        client: Client,
        userId: string,
        username: string,
        discordUserId?: string
    ): Promise<{ channel: TextChannel | null; isNew: boolean }> {
        try {
            // First, try to find an existing cart channel
            const existingChannel = await this.findExistingCartChannel(guild, userId, discordUserId);

            if (existingChannel) {
                console.log(`Using existing cart channel for user ${userId}: ${existingChannel.name}`);
                return { channel: existingChannel, isNew: false };
            }

            // No existing channel found, create a new one
            console.log(`Creating new cart channel for user ${userId}`);
            const newChannel = await this.createCartChannel(guild, client, userId, username, discordUserId);

            if (newChannel) {
                console.log(`Created new cart channel for user ${userId}: ${newChannel.name}`);
                return { channel: newChannel, isNew: true };
            }

            console.error(`Failed to create cart channel for user ${userId}`);
            return { channel: null, isNew: false };
        } catch (error) {
            console.error('Error getting or creating cart channel:', error);
            return { channel: null, isNew: false };
        }
    }

    /**
     * Restore all carts from existing cart channels (for bot startup)
     */
    static async restoreAllCarts(guild: Guild): Promise<void> {
        try {
            console.log('Restoring carts from existing cart channels...');

            // Fetch all channels to ensure cache is up to date
            await guild.channels.fetch();

            // Get all text channels that contain "cart" in the name
            const cartChannels = guild.channels.cache.filter(channel =>
                channel.type === ChannelType.GuildText &&
                channel.name.includes('cart')
            );

            console.log(`Found ${cartChannels.size} cart channels to restore`);

            for (const [, channel] of cartChannels) {
                const textChannel = channel as TextChannel;

                try {
                    // Get the user ID from the first message
                    const userId = await this.getUserIdFromCartChannel(textChannel);

                    if (userId) {
                        console.log(`Restoring cart for user ${userId} from channel ${textChannel.name}`);

                        // Recreate the cart from channel messages
                        await CartSummaryService.recreateCartFromChannel(
                            textChannel,
                            userId,
                            guild.id
                        );
                    }
                } catch (error) {
                    console.error(`Error restoring cart from channel ${textChannel.name}:`, error);
                }
            }

            console.log('Cart restoration complete');
        } catch (error) {
            console.error('Error restoring carts:', error);
        }
    }

    /**
     * Get the first message in a channel (searches all messages if needed)
     */
    private static async getFirstMessage(channel: TextChannel): Promise<any> {
        try {
            console.log(`🔍 Searching for first message in ${channel.name}...`);
            let oldestMessage: any = null;
            let lastMessageId: string | undefined;
            let searchedCount = 0;
            const maxSearch = 2000; // Limit to prevent excessive API calls

            while (searchedCount < maxSearch) {
                const messages = await channel.messages.fetch({
                    limit: 100,
                    before: lastMessageId,
                    cache: false
                });

                if (messages.size === 0) break;

                // The last message in the fetched batch is the oldest
                const batchOldest = messages.last();
                if (batchOldest) {
                    oldestMessage = batchOldest;
                }

                searchedCount += messages.size;
                lastMessageId = messages.last()?.id;

                // Add small delay to prevent rate limiting
                if (searchedCount % 500 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            if (oldestMessage) {
                console.log(`📍 Found first message in ${channel.name} after searching ${searchedCount} messages`);
            } else {
                console.log(`❌ No first message found in ${channel.name} after searching ${searchedCount} messages`);
            }

            return oldestMessage;
        } catch (error) {
            console.error('Error getting first message:', error);
            return null;
        }
    }

    /**
     * Extract user ID from cart channel's first message
     */
    private static async getUserIdFromCartChannel(channel: TextChannel): Promise<string | null> {
        try {
            // Use the new getFirstMessage method to find the actual first message
            const firstMessage = await this.getFirstMessage(channel);

            if (!firstMessage) return null;

            // Extract user ID from mention in the first message
            const userMentionMatch = firstMessage.content.match(/<@(\d+)>/);

            return userMentionMatch ? userMentionMatch[1] : null;
        } catch (error) {
            console.error('Error extracting user ID from cart channel:', error);
            return null;
        }
    }

    /**
     * Close a ticket channel with automatic transcript saving and a delay
     */
    static async closeTicketChannel(
        channel: TextChannel,
        closedBy?: User,
        delayMs: number = 5000
    ): Promise<void> {
        try {
            await channel.send('This ticket is now being closed...');

            // Save transcript automatically before closing
            if (closedBy && channel.guild) {
                try {
                    await channel.send('📝 Saving transcript before closing...');
                    console.log(`Auto-saving transcript for channel: ${channel.name} before closing`);

                    const transcriptSuccess = await TranscriptService.saveTicketTranscript(
                        channel.guild,
                        channel,
                        closedBy,
                        true // isAutoSave = true
                    );

                    if (transcriptSuccess) {
                        await channel.send('✅ Transcript saved successfully!');
                        console.log(`Transcript auto-saved successfully for channel: ${channel.name}`);
                    } else {
                        await channel.send('⚠️ Failed to save transcript, but proceeding with channel closure.');
                        console.warn(`Failed to auto-save transcript for channel: ${channel.name}`);
                    }
                } catch (transcriptError) {
                    console.error('Error auto-saving transcript:', transcriptError);
                    await channel.send('⚠️ Error saving transcript, but proceeding with channel closure.');
                }
            }

            setTimeout(async () => {
                try {
                    await channel.delete();
                } catch (error) {
                    console.error('Error deleting channel:', error);
                }
            }, delayMs);
        } catch (error) {
            console.error('Error closing ticket channel:', error);
            throw error;
        }
    }
}
