import { EmbedBuilder, Message } from "discord.js";
import { TextToggleManager } from "./text-toggle-manager";
import { BrandingManager } from "./branding-manager";
import { ArkDbHelper } from "../utils/arkDbHelper";
import { capitalize } from "../utils/utils";

/**
 * EmbedUpdateService - <PERSON>les updating existing embeds while preserving content
 * This service modifies existing embeds instead of recreating them from scratch
 */
export class EmbedUpdateService {
    /**
     * Extract listing ID from embed title or URL
     */
    private static extractListingId(originalEmbed: any): number | null {
        let listingId: number | null = null;
        if (originalEmbed.title) {
            const titleMatch = originalEmbed.title.match(/#(\d+)/);
            if (titleMatch) {
                listingId = parseInt(titleMatch[1]);
            }
        }
        if (!listingId && originalEmbed.url) {
            const urlMatch = originalEmbed.url.match(/\/listings\/(\d+)\//);
            if (urlMatch) {
                listingId = parseInt(urlMatch[1]);
            }
        }
        return listingId;
    }

    /**
     * Update an existing embed with text toggle changes
     */
    static async updateEmbedWithTextToggles(
        originalEmbed: any,
        guildId: string,
        channelId: string,
        messageId: string,
        passedListingId?: string
    ): Promise<EmbedBuilder> {
        // Check link toggle state first
        const linkDisabled = BrandingManager.isLinkDisabled(guildId, channelId, messageId);

        // Create a new embed builder from the original embed
        const updatedEmbed = new EmbedBuilder()
            .setTitle(originalEmbed.title)
            .setColor(originalEmbed.color);

        // Handle URL based on link toggle - set it conditionally from the start
        if (!linkDisabled) {
            let urlToSet = originalEmbed.url;

            // If URL is missing but link should be enabled, reconstruct it from listing ID
            if (!urlToSet) {
                // Use passed listing ID first, fallback to extraction from embed
                const idToUse = passedListingId || this.extractListingId(originalEmbed);
                if (idToUse) {
                    urlToSet = `https://wikily.gg/ark-survival-ascended/trading/listings/${idToUse}/`;
                }
            }

            if (urlToSet) {
                updatedEmbed.setURL(urlToSet);
            }
        }

        // Handle description based on toggle state
        const descriptionDisabled = TextToggleManager.isDescriptionTextDisabled(guildId, channelId, messageId);
        if (!descriptionDisabled) {
            if (originalEmbed.description) {
                updatedEmbed.setDescription(originalEmbed.description);
            } else {
                // If description is missing but should be shown, fetch from database
                const listingId = this.extractListingId(originalEmbed);
                if (listingId) {
                    try {
                        const listingData = await ArkDbHelper.getListingById(listingId);
                        if (listingData && listingData.description) {
                            updatedEmbed.setDescription(listingData.description);
                        } else {
                            updatedEmbed.setDescription('No description provided');
                        }
                    } catch (error) {
                        console.error('Error fetching listing description:', error);
                        updatedEmbed.setDescription('No description provided');
                    }
                }
            }
        }

        // Preserve images
        if (originalEmbed.image?.url) {
            updatedEmbed.setImage(originalEmbed.image.url);
        }
        if (originalEmbed.thumbnail?.url) {
            updatedEmbed.setThumbnail(originalEmbed.thumbnail.url);
        }

        // Handle fields - preserve existing fields and recreate missing ones from database
        const fieldsToAdd = [];

        // Extract listing ID from embed title or URL
        const listingId = this.extractListingId(originalEmbed);

        // Get toggle states
        const typeDisabled = TextToggleManager.isTypeTextDisabled(guildId, channelId, messageId);
        const categoryDisabled = TextToggleManager.isCategoryTextDisabled(guildId, channelId, messageId);
        const clusterDisabled = TextToggleManager.isClusterTextDisabled(guildId, channelId, messageId);

        // Check which fields exist in the original embed
        const existingFields = new Map();
        if (originalEmbed.fields) {
            for (const field of originalEmbed.fields) {
                const fieldName = field.name?.toLowerCase() || '';
                existingFields.set(fieldName, field);
            }
        }

        // If we need to show fields that are missing, fetch listing data
        let listingData = null;
        if (listingId && ((!typeDisabled && !existingFields.has('type')) ||
                         (!categoryDisabled && !existingFields.has('category')) ||
                         (!clusterDisabled && !existingFields.has('cluster')))) {
            try {
                listingData = await ArkDbHelper.getListingById(parseInt(listingId.toString()));
            } catch (error) {
                console.error('Error fetching listing data for field restoration:', error);
            }
        }

        // Add Type field
        if (!typeDisabled) {
            if (existingFields.has('type')) {
                fieldsToAdd.push(existingFields.get('type'));
            } else if (listingData) {
                const listingType = listingData.listing_type === 'sell' ? `🛒 Selling` : '💰 Buying';
                fieldsToAdd.push({ name: 'Type', value: listingType, inline: true });
            }
        }

        // Add Category field
        if (!categoryDisabled) {
            if (existingFields.has('category')) {
                fieldsToAdd.push(existingFields.get('category'));
            } else if (listingData) {
                fieldsToAdd.push({ name: 'Category', value: capitalize(listingData.category) || 'N/A', inline: true });
            }
        }

        // Add Cluster field
        if (!clusterDisabled) {
            if (existingFields.has('cluster')) {
                fieldsToAdd.push(existingFields.get('cluster'));
            } else if (listingData) {
                fieldsToAdd.push({ name: 'Cluster', value: listingData.cluster || 'N/A', inline: true });
            }
        }

        // Add any other fields that aren't Type, Category, or Cluster (like Price)
        if (originalEmbed.fields) {
            for (const field of originalEmbed.fields) {
                const fieldName = field.name?.toLowerCase() || '';
                if (!fieldName.includes('type') && !fieldName.includes('category') && !fieldName.includes('cluster')) {
                    fieldsToAdd.push({
                        name: field.name,
                        value: field.value,
                        inline: field.inline
                    });
                }
            }
        }

        if (fieldsToAdd.length > 0) {
            updatedEmbed.addFields(...fieldsToAdd);
        }

        // Handle footer with custom branding
        const brandingDisabled = BrandingManager.isBrandingDisabled(guildId, channelId, messageId);
        if (!brandingDisabled) {
            const customFooterText = BrandingManager.getCustomFooterText(guildId, channelId, messageId);
            const customLogoUrl = BrandingManager.getCustomLogoUrl(guildId, channelId, messageId);

            const footerText = customFooterText || originalEmbed.footer?.text || `Powered by\nWikily`;
            const logoUrl = customLogoUrl || originalEmbed.footer?.iconURL || 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png';

            updatedEmbed.setFooter({
                text: footerText,
                iconURL: logoUrl
            });
        } else if (originalEmbed.footer) {
            // Keep original footer if branding not disabled and no custom branding
            updatedEmbed.setFooter({
                text: originalEmbed.footer.text,
                iconURL: originalEmbed.footer.iconURL
            });
        }

        return updatedEmbed;
    }

    /**
     * Update an existing embed with branding changes only
     */
    static updateEmbedWithBranding(
        originalEmbed: any,
        guildId: string,
        channelId: string,
        messageId: string,
        passedListingId?: string
    ): EmbedBuilder {
        // Check link toggle state first
        const linkDisabled = BrandingManager.isLinkDisabled(guildId, channelId, messageId);

        // Create a new embed builder preserving all original content
        const updatedEmbed = new EmbedBuilder()
            .setTitle(originalEmbed.title)
            .setColor(originalEmbed.color)
            .setDescription(originalEmbed.description);

        // Handle URL based on link toggle - set it conditionally from the start
        if (!linkDisabled) {
            let urlToSet = originalEmbed.url;

            // If URL is missing but link should be enabled, reconstruct it from listing ID
            if (!urlToSet) {
                // Use passed listing ID first, fallback to extraction from embed
                const idToUse = passedListingId || this.extractListingId(originalEmbed);
                if (idToUse) {
                    urlToSet = `https://wikily.gg/ark-survival-ascended/trading/listings/${idToUse}/`;
                }
            }

            if (urlToSet) {
                updatedEmbed.setURL(urlToSet);
            }
        }

        // Preserve images
        if (originalEmbed.image?.url) {
            updatedEmbed.setImage(originalEmbed.image.url);
        }
        if (originalEmbed.thumbnail?.url) {
            updatedEmbed.setThumbnail(originalEmbed.thumbnail.url);
        }

        // Preserve all fields
        if (originalEmbed.fields && originalEmbed.fields.length > 0) {
            updatedEmbed.addFields(...originalEmbed.fields);
        }

        // Update only the footer/branding
        const brandingDisabled = BrandingManager.isBrandingDisabled(guildId, channelId, messageId);

        if (!brandingDisabled) {
            const customFooterText = BrandingManager.getCustomFooterText(guildId, channelId, messageId);
            const customLogoUrl = BrandingManager.getCustomLogoUrl(guildId, channelId, messageId);

            const footerText = customFooterText || `Powered by\nWikily`;
            const logoUrl = customLogoUrl || 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png';

            updatedEmbed.setFooter({
                text: footerText,
                iconURL: logoUrl
            });
        }

        return updatedEmbed;
    }

    /**
     * Find and update a listing message in a channel
     */
    static async findAndUpdateListingMessage(
        channel: any,
        listingId: string,
        updateType: 'text' | 'branding',
        botUserId: string
    ): Promise<boolean> {
        try {
            if (!channel || !('messages' in channel)) {
                return false;
            }

            // Search for the listing message
            let listingMessage: Message | undefined;
            let searchedCount = 0;
            const maxSearch = 200;
            let lastMessageId: string | undefined;

            while (!listingMessage && searchedCount < maxSearch) {
                const messages = await channel.messages.fetch({
                    limit: 50,
                    before: lastMessageId
                });

                if (messages.size === 0) break;

                listingMessage = messages.find((msg: Message) => {
                    if (msg.author.id !== botUserId || msg.embeds.length === 0) {
                        return false;
                    }

                    const embed = msg.embeds[0];
                    if (!embed || !embed.title) {
                        return false;
                    }

                    // Check multiple possible title formats
                    const titleChecks = [
                        embed.title.includes(`#${listingId}`),
                        embed.title.includes(`Listing #${listingId}`),
                        embed.title.includes(`Listing ${listingId}`),
                        embed.title.endsWith(`#${listingId}`)
                    ];

                    // Also check if the message has the manage button for this listing
                    const hasManageButton = msg.components?.some((row: any) =>
                        row.components?.some((component: any) =>
                            component.customId === `admin_manage_${listingId}`
                        )
                    );

                    return titleChecks.some(check => check) || hasManageButton;
                }) as Message | undefined;

                lastMessageId = messages.last()?.id;
                searchedCount += messages.size;
            }

            if (listingMessage && listingMessage.editable) {
                const originalEmbed = listingMessage.embeds[0];
                if (!originalEmbed) return false;

                let updatedEmbed: EmbedBuilder;

                if (updateType === 'text') {
                    updatedEmbed = await this.updateEmbedWithTextToggles(
                        originalEmbed,
                        listingMessage.guild?.id || '',
                        listingMessage.channel.id,
                        listingMessage.id,
                        listingId
                    );
                } else {
                    updatedEmbed = this.updateEmbedWithBranding(
                        originalEmbed,
                        listingMessage.guild?.id || '',
                        listingMessage.channel.id,
                        listingMessage.id,
                        listingId
                    );
                }

                // Handle attachment regeneration if needed
                const editOptions: any = {
                    embeds: [updatedEmbed],
                    components: listingMessage.components
                };

                // Check if the embed uses attachment URLs
                const hasAttachmentImage = originalEmbed.image?.url?.startsWith('attachment://');
                const hasAttachmentThumbnail = originalEmbed.thumbnail?.url?.startsWith('attachment://');

                if (hasAttachmentImage || hasAttachmentThumbnail) {
                    // Extract listing ID and regenerate attachment
                    const listingId = this.extractListingId(originalEmbed);
                    if (listingId) {
                        const { ImageService } = await import('./image-service.js');
                        const regeneratedAttachment = await ImageService.createListingPreviewImage(listingId);

                        if (regeneratedAttachment) {
                            editOptions.files = [regeneratedAttachment];
                        } else {
                            // If regeneration fails, clear the attachment URL and use no image
                            if (hasAttachmentImage) {
                                updatedEmbed.setImage(null);
                            }
                            if (hasAttachmentThumbnail) {
                                updatedEmbed.setThumbnail(null);
                            }
                            editOptions.files = [];
                        }
                    } else {
                        editOptions.files = [];
                    }
                } else {
                    editOptions.files = [];
                    editOptions.attachments = [];
                }

                await listingMessage.edit(editOptions);

                return true;
            }

            return false;
        } catch (error) {
            console.error('Error finding and updating listing message:', error);
            return false;
        }
    }
}
