import {
    ModalSubmitInteraction
} from "discord.js";
import { cartManager } from "../../utils/cartManager";
import { ChannelService } from "../../services/channel-service";
import { CartSummaryService } from "../../services/cart-summary-service";
import { NumberFormatter } from "../../utils/numberFormatter";
import pool from "../../utils/dbConfig";
import { GuildRoleHelper } from "../../utils/guildRoleHelper";

/**
 * Handle shop quantity modal submission
 */
export async function handleShopQuantityModal(
    interaction: ModalSubmitInteraction,
    listingId: string,
    paymentId: string,
    _interactingUserId?: string
): Promise<void> {
    await interaction.deferUpdate();

    const quantityInput = interaction.fields.getTextInputValue('quantity');
    const quantity = parseInt(quantityInput);

    if (isNaN(quantity) || quantity <= 0) {
        await interaction.followUp({
            content: 'Please enter a valid positive number for quantity.',
            ephemeral: true
        });
        return;
    }

    const guildId = interaction.guild?.id;
    if (!guildId) {
        await interaction.followUp({
            content: 'This command can only be used in a server.',
            ephemeral: true
        });
        return;
    }

    try {
        // Fetch listing details
        const listingQuery = `
            SELECT l.*
            FROM asa.listings l
            WHERE l.listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.followUp({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];

        // Fetch payment details if paymentId is provided and not 'contact'
        let paymentMethod = 'Contact seller for details';
        let paymentIdNumber: number | undefined = undefined;

        if (paymentId !== 'contact') {
            try {
                const paymentIdAsNumber = parseInt(paymentId);

                const paymentQuery = `
                    SELECT p.*, i.name as item_name
                    FROM asa.payment_options p
                    LEFT JOIN asa.items i ON SPLIT_PART(p.item_id, ' ', 1) = SPLIT_PART(i.id, ' ', 1)
                    WHERE p.payment_id = $1
                `;

                const paymentResult = await pool.query(paymentQuery, [paymentIdAsNumber]);

                if (paymentResult.rows.length > 0) {
                    const paymentData = paymentResult.rows[0];
                    paymentIdNumber = paymentIdAsNumber;

                    if (paymentData.item_name) {
                        const formattedQuantity = NumberFormatter.formatWithCommas(paymentData.quantity);
                        paymentMethod = `${paymentData.item_name} x${formattedQuantity}`;
                    } else if (paymentData.custom_price) {
                        paymentMethod = paymentData.custom_price;
                    }
                }
            } catch (err) {
                console.error('Error parsing payment ID or querying database:', err);
            }
        }

        // Add to cart for the Discord user who clicked
        cartManager.addItem(interaction.user.id, guildId, {
            listingId: parseInt(listingId),
            title: listing.title || `Listing #${listingId}`,
            quantity: quantity,
            paymentMethod,
            paymentId: paymentIdNumber,
            listingType: listing.listing_type
        });

        // Get or create cart channel for the user who clicked
        if (interaction.guild) {
            const { channel, isNew: isNewChannel } = await ChannelService.getOrCreateCartChannel(
                interaction.guild,
                interaction.client,
                interaction.user.id,
                interaction.user.username
            );

            if (!channel) {
                await interaction.followUp({
                    content: 'Error: Could not create or access cart channel.',
                    ephemeral: true
                });
                return;
            }

            if (isNewChannel) {
                // Get cart notify roles to tag
                const notifyRoles = await GuildRoleHelper.getGuildRoles(interaction.guild.id, 'cart_notify');

                // Create notification content
                let notificationContent = `🛒 **Cart Channel Created**\n${interaction.user}, this is your personal cart channel! All your trades will be managed here.`;

                // Add role notifications if any are configured
                if (notifyRoles.length > 0) {
                    const roleMentions = notifyRoles.map(roleId => `<@&${roleId}>`).join(' ');
                    notificationContent += `\n\n🔔 **New Customer Alert:** ${roleMentions}`;
                }

                // Send initial message with user tag for identification
                await channel.send({
                    content: notificationContent
                });
            }

            // Add individual item message
            const itemData = CartSummaryService.createItemEmbed(
                listing.title || `Listing #${listingId}`,
                quantity,
                paymentMethod,
                interaction.user.toString(),
                listing.listing_type
            );

            await channel.send({
                embeds: [itemData.embed],
                components: itemData.components
            });

            // Update the cart summary at the end of the channel
            await CartSummaryService.updateCartSummary(
                channel,
                interaction.user.id,
                guildId,
                interaction.user.toString()
            );

            // Notify the user
            const notificationMessage = isNewChannel
                ? `Item added to cart! Your cart channel has been created in ${channel}.`
                : `Item added to your existing cart channel ${channel}.`;

            await interaction.followUp({
                content: notificationMessage,
                ephemeral: true
            });
        }

    } catch (error) {
        console.error('Error in shop quantity modal handler:', error);
        await interaction.followUp({
            content: 'An error occurred while processing your request. Please try again later.',
            ephemeral: true
        });
    }
}
