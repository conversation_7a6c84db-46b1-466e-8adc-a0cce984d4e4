import {
    <PERSON><PERSON>ow<PERSON><PERSON>er,
    StringSelectMenuBuilder,
    StringSelectMenuInteraction
} from "discord.js";
import pool from "../../utils/dbConfig";

/**
 * Handle category selection for checkout creation
 */
export async function handleCategorySelection(
    interaction: StringSelectMenuInteraction,
    userId: string,
    discordUserId?: string
): Promise<void> {
    await interaction.deferUpdate();

    const selectedCategory = interaction.values[0];

    try {
        // Get user profile information
        const profileQuery = `
            SELECT username
            FROM public.profiles
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [userId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        // Query listings for the selected category and user
        const listingsQuery = `
            SELECT listing_id, title, listing_type, category, availability
            FROM asa.listings
            WHERE user_id = $1
            AND category = $2
            AND (availability IS NULL OR availability != 'not-available')
            ORDER BY created_at DESC
            LIMIT 25
        `;

        const listingsResult = await pool.query(listingsQuery, [userId, selectedCategory]);

        // Create listing selection menu
        const customId = discordUserId ? `checkout_listing_${userId}_${discordUserId}` : `checkout_listing_${userId}`;
        const listingSelect = new StringSelectMenuBuilder()
            .setCustomId(customId)
            .setPlaceholder('Select a listing or discuss in ticket');

        // Add "Discuss in Ticket" as the first option
        listingSelect.addOptions({
            label: 'Discuss in Ticket',
            value: 'discuss_ticket',
            emoji: '💬',
            description: 'Create a general discussion ticket'
        });

        if (listingsResult.rows.length > 0) {
            // Add listings as options
            for (const listing of listingsResult.rows) {
                const listingTitle = listing.title || `Listing #${listing.listing_id}`;
                const typeEmoji = listing.listing_type === 'buy' ? '💰' : '🛒';
                const availabilityText = listing.availability === 'soon' ? ' (Soon)' : 
                                       listing.availability === 'unofficial' ? ' (Unofficial)' : '';
                
                listingSelect.addOptions({
                    label: `${listingTitle}${availabilityText}`.substring(0, 100),
                    value: listing.listing_id.toString(),
                    emoji: typeEmoji,
                    description: `${listing.listing_type === 'buy' ? 'Buying' : 'Selling'} - ${selectedCategory}`.substring(0, 100)
                });
            }
        }

        // If no listings found, show appropriate message
        if (listingsResult.rows.length === 0) {
            listingSelect.addOptions({
                label: 'No listings found in this category',
                value: 'no_listings',
                emoji: '❌',
                description: 'This user has no available listings in this category'
            });
        }

        const listingRow = new ActionRowBuilder<StringSelectMenuBuilder>()
            .addComponents(listingSelect);

        const categoryEmojis: { [key: string]: string } = {
            'item': '🎒',
            'dino': '🦕',
            'egg': '🥚',
            'blueprint': '📋',
            'boss_fight': '⚔️',
            'base_spot': '🏠',
            'xp_party': '🎉',
            'other': '❓',
            'bundle': '📦'
        };

        const categoryName = selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1).replace('_', ' ');
        const emoji = categoryEmojis[selectedCategory] || '📋';

        await interaction.editReply({
            content: `${emoji} **${categoryName} Listings for** **${username}**\n\nFound **${listingsResult.rows.length}** listing(s). Select one to proceed:`,
            components: [listingRow]
        });

    } catch (error) {
        console.error('Error in category selection handler:', error);
        await interaction.followUp({
            content: 'An error occurred while fetching listings. Please try again later.',
            ephemeral: true
        });
    }
}
