import {
    ModalBuilder,
    StringSelectMenuInteraction,
    TextInputBuilder,
    TextInputStyle,
    ActionRowBuilder
} from "discord.js";
import pool from "../../utils/dbConfig";

/**
 * Handle payment selection for checkout creation
 */
export async function handleCheckoutPaymentSelection(
    interaction: StringSelectMenuInteraction,
    listingId: string,
    databaseUserId: string,
    discordUserId?: string
): Promise<void> {
    const paymentId = interaction.values[0];

    try {
        // Get listing information to determine type
        const listingQuery = `
            SELECT listing_type, title
            FROM asa.listings 
            WHERE listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.reply({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];
        const isBuyingListing = listing.listing_type === 'buy';

        // Create and show the quantity modal with contextual text
        const modal = new ModalBuilder()
            .setCustomId(`checkout_quantity_${listingId}_${paymentId}_${databaseUserId}_${discordUserId}`)
            .setTitle(isBuyingListing ? 'Enter Quantity to Sell' : 'Enter Quantity to Buy');

        const quantityLabel = isBuyingListing
            ? 'How many would you like to sell?'
            : 'How many would you like to buy?';

        const quantityInput = new TextInputBuilder()
            .setCustomId('quantity')
            .setLabel(quantityLabel)
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a number')
            .setRequired(true)
            .setValue('1')
            .setMinLength(1)
            .setMaxLength(7);

        const quantityRow = new ActionRowBuilder<TextInputBuilder>()
            .addComponents(quantityInput);

        modal.addComponents(quantityRow);

        await interaction.showModal(modal);

    } catch (error) {
        console.error('Error in checkout payment selection handler:', error);
        await interaction.reply({
            content: 'An error occurred while processing your selection. Please try again later.',
            ephemeral: true
        });
    }
}
