import {
    ActionRowBuilder,
    ApplicationCommandOptionType,
    ApplicationCommandType,
    Client,
    CommandInteraction,
    StringSelectMenuBuilder,
    EmbedBuilder
} from "discord.js";
import { Command } from "../../types";
import pool from "../../utils/dbConfig";

export const createshop: Command = {
    name: "createshop",
    description: "Create a permanent shop embed for a user's listings",
    category: "trading",
    type: ApplicationCommandType.ChatInput,
    options: [
        {
            name: "username",
            description: "The username to create shop for (from Wikily profiles)",
            type: ApplicationCommandOptionType.String,
            required: true
        },
        {
            name: "title",
            description: "Custom title for the shop embed",
            type: ApplicationCommandOptionType.String,
            required: true
        }
    ],
    run: async (_client: Client, interaction: CommandInteraction) => {
        await interaction.deferReply();

        const username = interaction.options.get("username")?.value as string;
        const title = interaction.options.get("title")?.value as string;

        if (!username) {
            await interaction.editReply({ content: "Please provide a valid username." });
            return;
        }

        if (!title) {
            await interaction.editReply({ content: "Please provide a valid title." });
            return;
        }

        try {
            // Look up the user ID from the profiles table
            const profileQuery = `
                SELECT id, username
                FROM public.profiles
                WHERE LOWER(username) = LOWER($1)
            `;

            const profileResult = await pool.query(profileQuery, [username]);

            if (profileResult.rows.length === 0) {
                await interaction.editReply({
                    content: `❌ No user found with username: **${username}**\n\nPlease make sure the username is spelled correctly and exists in the Wikily database.`
                });
                return;
            }

            const userProfile = profileResult.rows[0];
            const userId = userProfile.id; // This is the UUID from the profiles table

            // Check how many listings this user has
            const listingCountQuery = `
                SELECT COUNT(*) as count
                FROM asa.listings
                WHERE user_id = $1
                AND (availability IS NULL OR availability != 'not-available')
            `;

            const countResult = await pool.query(listingCountQuery, [userId]);
            const listingCount = parseInt(countResult.rows[0].count);

            // Create the permanent shop embed
            const shopEmbed = new EmbedBuilder()
                .setTitle(`🛒 ${title}`)
                .setDescription(`Browse and purchase items from **${userProfile.username}**'s collection.\n\n📦 **${listingCount}** listings available\n\nSelect a category below to get started!`)
                .setColor('#00ff88')
                .setTimestamp()
                .setFooter({
                    text: 'This shop is always available • Select a category to browse',
                    iconURL: interaction.client.user?.displayAvatarURL()
                });

            // Create persistent category selection menu
            const categorySelect = new StringSelectMenuBuilder()
                .setCustomId(`shop_category_${userId}`)
                .setPlaceholder('🏪 Select a category to browse listings')
                .addOptions([
                    {
                        label: 'Create Ticket',
                        value: 'create_ticket',
                        emoji: '🎫',
                        description: 'Start a conversation without purchasing'
                    },
                    {
                        label: 'Items',
                        value: 'item',
                        emoji: '🎒',
                        description: 'General items and resources'
                    },
                    {
                        label: 'Dinosaurs',
                        value: 'dino',
                        emoji: '🦕',
                        description: 'Tamed dinosaurs and creatures'
                    },
                    {
                        label: 'Eggs',
                        value: 'egg',
                        emoji: '🥚',
                        description: 'Fertilized eggs for breeding'
                    },
                    {
                        label: 'Blueprints',
                        value: 'blueprint',
                        emoji: '📋',
                        description: 'Item blueprints and schematics'
                    },
                    {
                        label: 'Boss Fights',
                        value: 'boss_fight',
                        emoji: '⚔️',
                        description: 'Boss fight services and carries'
                    },
                    {
                        label: 'Base Spots',
                        value: 'base_spot',
                        emoji: '🏠',
                        description: 'Base locations and spots'
                    },
                    {
                        label: 'XP Parties',
                        value: 'xp_party',
                        emoji: '🎉',
                        description: 'Experience farming parties'
                    },
                    {
                        label: 'Other',
                        value: 'other',
                        emoji: '❓',
                        description: 'Miscellaneous services and items'
                    },
                    {
                        label: 'Bundles',
                        value: 'bundle',
                        emoji: '📦',
                        description: 'Item bundles and packages'
                    }
                ]);

            const categoryRow = new ActionRowBuilder<StringSelectMenuBuilder>()
                .addComponents(categorySelect);

            // Send the permanent shop embed to the channel (replace the deferred reply)
            await interaction.editReply({
                embeds: [shopEmbed],
                components: [categoryRow]
            });

            // Send ephemeral confirmation to admin
            await interaction.followUp({
                content: `✅ **Shop created successfully!**\n\n🛒 **${title}** has been created in this channel.\n\nUsers can now browse and purchase from **${userProfile.username}**'s ${listingCount} available listings using the dropdown menu.`,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error in createcheckout command:', error);
            await interaction.editReply({
                content: 'An error occurred while processing your request. Please try again later.'
            });
        }
    }
};
