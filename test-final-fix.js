/**
 * Final comprehensive test for Toggle Link functionality
 */

const { BrandingManager } = require('./dist/services/branding-manager.js');
const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

// Real-world scenario: embed without listing ID in title
const realWorldEmbed = {
    title: 'Premium ARK Dinos & Items',
    color: 0x00ff00,
    description: 'High quality breeding lines available',
    url: 'https://wikily.gg/ark-survival-ascended/trading/listings/12345/',
    fields: [{ name: 'Price', value: '1000 Metal Ingot', inline: true }]
};

const guildId = 'test-guild';
const channelId = 'test-channel';
const messageId = 'test-message';

console.log('🧪 Final Comprehensive Toggle Link Test\n');

// Test 1: Start with URL, disable link
console.log('Test 1: Disable link (URL should be removed)');
BrandingManager.disableLink(guildId, channelId, messageId);
const disabledResult = EmbedUpdateService.updateEmbedWithBranding(
    realWorldEmbed,
    guildId,
    channelId,
    messageId,
    '12345' // Listing ID from button
);
console.log(`URL after disable: ${disabledResult.data.url}`);
console.log(`✅ Test 1 ${disabledResult.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Re-enable link - should reconstruct URL using passed listing ID
console.log('Test 2: Re-enable link (should reconstruct URL)');
BrandingManager.enableLink(guildId, channelId, messageId);

// Simulate the embed after it was disabled (no URL)
const embedWithoutUrl = {
    title: 'Premium ARK Dinos & Items',
    color: 0x00ff00,
    description: 'High quality breeding lines available',
    // url: undefined (removed when disabled)
    fields: [{ name: 'Price', value: '1000 Metal Ingot', inline: true }]
};

const reenabledResult = EmbedUpdateService.updateEmbedWithBranding(
    embedWithoutUrl,
    guildId,
    channelId,
    messageId,
    '12345' // Listing ID from button - this is the key fix!
);
console.log(`URL after re-enable: ${reenabledResult.data.url}`);
const expectedUrl = 'https://wikily.gg/ark-survival-ascended/trading/listings/12345/';
console.log(`Expected: ${expectedUrl}`);
console.log(`✅ Test 2 ${reenabledResult.data.url === expectedUrl ? 'PASSED' : 'FAILED'}\n`);

// Test 3: Toggle multiple times to ensure consistency
console.log('Test 3: Multiple toggles');
let toggleResult;

// Disable
BrandingManager.disableLink(guildId, channelId, messageId);
toggleResult = EmbedUpdateService.updateEmbedWithBranding(
    embedWithoutUrl, guildId, channelId, messageId, '12345'
);
const disabled1 = toggleResult.data.url === undefined;

// Enable
BrandingManager.enableLink(guildId, channelId, messageId);
toggleResult = EmbedUpdateService.updateEmbedWithBranding(
    embedWithoutUrl, guildId, channelId, messageId, '12345'
);
const enabled1 = toggleResult.data.url === expectedUrl;

// Disable again
BrandingManager.disableLink(guildId, channelId, messageId);
toggleResult = EmbedUpdateService.updateEmbedWithBranding(
    embedWithoutUrl, guildId, channelId, messageId, '12345'
);
const disabled2 = toggleResult.data.url === undefined;

// Enable again
BrandingManager.enableLink(guildId, channelId, messageId);
toggleResult = EmbedUpdateService.updateEmbedWithBranding(
    embedWithoutUrl, guildId, channelId, messageId, '12345'
);
const enabled2 = toggleResult.data.url === expectedUrl;

console.log(`Multiple toggles work: ${disabled1 && enabled1 && disabled2 && enabled2}`);
console.log(`✅ Test 3 ${disabled1 && enabled1 && disabled2 && enabled2 ? 'PASSED' : 'FAILED'}\n`);

// Test 4: Verify other embed properties are preserved
console.log('Test 4: Verify embed properties preserved');
const titleOk = reenabledResult.data.title === embedWithoutUrl.title;
const colorOk = reenabledResult.data.color === embedWithoutUrl.color;
const descOk = reenabledResult.data.description === embedWithoutUrl.description;
const fieldsOk = reenabledResult.data.fields?.length === embedWithoutUrl.fields.length;

console.log(`Title: ${titleOk}, Color: ${colorOk}, Description: ${descOk}, Fields: ${fieldsOk}`);
console.log(`✅ Test 4 ${titleOk && colorOk && descOk && fieldsOk ? 'PASSED' : 'FAILED'}\n`);

// Test 5: Test with different listing ID formats
console.log('Test 5: Different listing ID formats');
const testIds = ['123', '456789', '1'];
let allIdTestsPassed = true;

for (const testId of testIds) {
    const result = EmbedUpdateService.updateEmbedWithBranding(
        embedWithoutUrl, guildId, channelId, messageId, testId
    );
    const expectedTestUrl = `https://wikily.gg/ark-survival-ascended/trading/listings/${testId}/`;
    if (result.data.url !== expectedTestUrl) {
        allIdTestsPassed = false;
        console.log(`Failed for ID ${testId}: got ${result.data.url}, expected ${expectedTestUrl}`);
    }
}

console.log(`All ID formats work: ${allIdTestsPassed}`);
console.log(`✅ Test 5 ${allIdTestsPassed ? 'PASSED' : 'FAILED'}\n`);

// Summary
const allTests = [
    disabledResult.data.url === undefined,
    reenabledResult.data.url === expectedUrl,
    disabled1 && enabled1 && disabled2 && enabled2,
    titleOk && colorOk && descOk && fieldsOk,
    allIdTestsPassed
];

const passedTests = allTests.filter(test => test).length;
console.log(`🎯 Final Summary: ${passedTests}/5 tests passed`);
console.log(`${passedTests === 5 ? '🎉 ALL TESTS PASSED! Toggle Link is fully fixed!' : '❌ SOME TESTS FAILED'}`);

if (passedTests === 5) {
    console.log('\n✨ The Toggle Link button now works correctly:');
    console.log('   • Can disable links (removes URL from embed)');
    console.log('   • Can re-enable links (reconstructs URL from listing ID)');
    console.log('   • Works even when listing ID is not in the embed title');
    console.log('   • Uses the listing ID from the button interaction');
    console.log('   • Preserves all other embed properties');
}

// Clean up
BrandingManager.clearAll();
console.log('\n🧹 Cleaned up test data');
