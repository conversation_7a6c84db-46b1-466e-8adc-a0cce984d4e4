/**
 * Test the listing ID fix for Toggle Link functionality
 */

const { BrandingManager } = require('./dist/services/branding-manager.js');
const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

// Test embed without listing ID in title (real-world scenario)
const mockEmbedNoIdInTitle = {
    title: 'Premium ARK Items for Sale',
    color: 0x00ff00,
    description: 'High quality items available',
    fields: [{ name: 'Price', value: '500 Metal Ingot', inline: true }]
};

// Test embed with listing ID in title (fallback scenario)
const mockEmbedWithIdInTitle = {
    title: 'Test Listing #456',
    color: 0x00ff00,
    description: 'Test description',
    fields: [{ name: 'Price', value: '100 Metal Ingot', inline: true }]
};

const guildId = 'test-guild';
const channelId = 'test-channel';
const messageId = 'test-message';

console.log('🧪 Testing Listing ID Fix for Toggle Link\n');

// Test 1: Embed without ID in title, using passed listing ID
console.log('Test 1: Embed without ID in title (real-world case)');
BrandingManager.enableLink(guildId, channelId, messageId);
const result1 = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedNoIdInTitle,
    guildId,
    channelId,
    messageId,
    '789' // Passed listing ID
);
console.log(`URL with passed ID: ${result1.data.url}`);
const expectedUrl1 = 'https://wikily.gg/ark-survival-ascended/trading/listings/789/';
console.log(`Expected: ${expectedUrl1}`);
console.log(`✅ Test 1 ${result1.data.url === expectedUrl1 ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Embed with ID in title, should still use passed listing ID (priority)
console.log('Test 2: Embed with ID in title, passed ID takes priority');
const result2 = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithIdInTitle,
    guildId,
    channelId,
    messageId,
    '999' // Passed listing ID should take priority over title ID (456)
);
console.log(`URL with passed ID (priority): ${result2.data.url}`);
const expectedUrl2 = 'https://wikily.gg/ark-survival-ascended/trading/listings/999/';
console.log(`Expected: ${expectedUrl2}`);
console.log(`✅ Test 2 ${result2.data.url === expectedUrl2 ? 'PASSED' : 'FAILED'}\n`);

// Test 3: No passed ID, fallback to title extraction
console.log('Test 3: No passed ID, fallback to title extraction');
const result3 = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithIdInTitle,
    guildId,
    channelId,
    messageId
    // No listing ID passed
);
console.log(`URL with title extraction: ${result3.data.url}`);
const expectedUrl3 = 'https://wikily.gg/ark-survival-ascended/trading/listings/456/';
console.log(`Expected: ${expectedUrl3}`);
console.log(`✅ Test 3 ${result3.data.url === expectedUrl3 ? 'PASSED' : 'FAILED'}\n`);

// Test 4: No passed ID, no ID in title, should fail gracefully
console.log('Test 4: No passed ID, no ID in title');
const result4 = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedNoIdInTitle,
    guildId,
    channelId,
    messageId
    // No listing ID passed, and no ID in title
);
console.log(`URL with no ID available: ${result4.data.url}`);
console.log(`Expected: undefined`);
console.log(`✅ Test 4 ${result4.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 5: Disable link - should remove URL regardless of ID availability
console.log('Test 5: Disable link - should remove URL');
BrandingManager.disableLink(guildId, channelId, messageId);
const result5 = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedNoIdInTitle,
    guildId,
    channelId,
    messageId,
    '789'
);
console.log(`URL when disabled: ${result5.data.url}`);
console.log(`Expected: undefined`);
console.log(`✅ Test 5 ${result5.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Summary
const allTests = [
    result1.data.url === expectedUrl1,
    result2.data.url === expectedUrl2,
    result3.data.url === expectedUrl3,
    result4.data.url === undefined,
    result5.data.url === undefined
];

const passedTests = allTests.filter(test => test).length;
console.log(`🎯 Summary: ${passedTests}/5 tests passed`);
console.log(`${passedTests === 5 ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);

// Clean up
BrandingManager.clearAll();
console.log('\n🧹 Cleaned up test data');
